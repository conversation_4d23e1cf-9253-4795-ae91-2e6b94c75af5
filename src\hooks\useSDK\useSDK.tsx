import { useMemo, useEffect } from "react";
import { baseUrl, OfflineService, operations } from "@/utils";
import MkdSDK from "@/utils/MkdSDK";
import TreeSDK from "@/utils/TreeSDK";
import { OfflineAwareMkdSDK } from "@/utils/offline/OfflineAwareMkdSDK";
import { OfflineAwareTreeSDK } from "@/utils/offline/OfflineAwareTreeSDK";
import { useOffline } from "@/hooks/useOffline";
import { AuthContext } from "@/context/Auth";
import { useContext } from "react";
import { customApiPaths } from "@/utils/customApiPaths";

interface SdkConfig {
  sdk?: MkdSDK;
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
  GOOGLE_CAPTCHA_SITEKEY?: string;
  enableOfflineMode?: boolean;
  role?: any;
}

export interface UseSDKReturnType {
  sdk: (OfflineAwareMkdSDK | MkdSDK) & {
    callCustomAPI: (
      path: string,
      method: "GET" | "POST" | "PUT" | "DELETE",
      payload: Record<string, any>
    ) => Promise<{ error: boolean; message: string }>;
    ebadollar: any;
  };
  tdk: OfflineAwareTreeSDK | TreeSDK;
  projectId: string;
  operations: typeof operations;
  isOfflineMode: boolean;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  // Try to get offline context, but don't require it
  // Note: useOffline must be called unconditionally to follow Rules of Hooks
  const offlineContext = useOffline();
  const { state } = useContext(AuthContext);

  const enableOfflineMode = config.enableOfflineMode ?? true;
  const hasOfflineService = offlineContext && enableOfflineMode;
  const offlineService = offlineContext?.offlineService;

  const sdkConfig = {
    // baseurl: import.meta.env.VITE_API_URL,
    baseurl: baseUrl,
    project_id: import.meta.env.VITE_PROJECT_ID,
  };

  const sdk = useMemo(() => {
    if (hasOfflineService) {
      return new OfflineAwareMkdSDK(sdkConfig, offlineService);
    }
    return new MkdSDK(sdkConfig);
  }, [sdkConfig, hasOfflineService]);

  const tdk = useMemo(() => {
    if (hasOfflineService) {
      return new OfflineAwareTreeSDK(sdkConfig, offlineService);
    }
    return new TreeSDK(sdkConfig);
  }, [sdkConfig, hasOfflineService]);

  // Add ebadollar namespace to SDK
  (sdk as any).ebadollar = customApiPaths.ebadollar;

  // Configure offline-aware SDKs with offline mode settings
  useEffect(() => {
    if (hasOfflineService && offlineContext) {
      const callCustomAPI = async (
        path: string,
        method: "GET" | "POST" | "PUT" | "DELETE",
        payload: Record<string, any>
      ) => {
        try {
          const response = await sdk.callCustomAPI(path, method, payload);
          return { error: false, message: response.message };
        } catch (error: any) {
          return { error: true, message: error.message };
        }
      };

      (sdk as any).callCustomAPI = callCustomAPI;
      if (state.isAuthenticated) {
        // Set token in localStorage instead of calling setToken
        localStorage.setItem("token", state.token || "");
      }
    }
  }, [
    hasOfflineService,
    offlineContext,
    sdk,
    state.isAuthenticated,
    state.token,
  ]);

  const projectId = sdk.getProjectId();

  return {
    sdk: sdk as (OfflineAwareMkdSDK | MkdSDK) & {
      callCustomAPI: (
        path: string,
        method: "GET" | "POST" | "PUT" | "DELETE",
        payload: Record<string, any>
      ) => Promise<{ error: boolean; message: string }>;
      ebadollar: any;
    },
    tdk,
    projectId,
    operations,
    isOfflineMode: hasOfflineService,
  };
};

export default useSDK;
